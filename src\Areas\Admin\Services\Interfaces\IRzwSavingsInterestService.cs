using RazeWinComTr.Areas.Admin.DbModel;

namespace RazeWinComTr.Areas.Admin.Services.Interfaces
{
    /// <summary>
    /// Interface for RZW Savings Interest Service operations
    /// </summary>
    public interface IRzwSavingsInterestService
    {
        /// <summary>
        /// Processes daily interest payments for all eligible accounts
        /// </summary>
        /// <returns>Number of processed accounts and total interest paid</returns>
        Task<(int ProcessedAccounts, decimal TotalInterestPaid)> ProcessDailyInterestAsync();

        /// <summary>
        /// Calculates daily compound interest for a savings account
        /// </summary>
        /// <param name="account">The savings account</param>
        /// <returns>Daily interest amount</returns>
        Task<decimal> CalculateDailyInterestAsync(RzwSavingsAccount account);

        /// <summary>
        /// Calculates interest for early withdrawal based on eligible plans
        /// </summary>
        /// <param name="account">The savings account</param>
        /// <returns>Interest amount for early withdrawal</returns>
        Task<decimal> CalculateEarlyWithdrawalInterestAsync(RzwSavingsAccount account);

        /// <summary>
        /// Pays interest to a savings account
        /// </summary>
        /// <param name="account">The savings account</param>
        /// <param name="interestAmount">Interest amount to pay</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> PayInterestAsync(RzwSavingsAccount account, decimal interestAmount);

        /// <summary>
        /// Calculates compound interest using the standard formula
        /// </summary>
        /// <param name="principal">Principal amount</param>
        /// <param name="dailyRate">Daily interest rate</param>
        /// <param name="days">Number of days</param>
        /// <returns>Compound interest amount</returns>
        decimal CalculateCompoundInterest(decimal principal, decimal dailyRate, int days);

        /// <summary>
        /// Gets interest payment history for a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="accountId">Optional account ID filter</param>
        /// <param name="fromDate">Optional from date filter</param>
        /// <param name="toDate">Optional to date filter</param>
        /// <param name="pageNumber">Page number for pagination</param>
        /// <param name="pageSize">Page size for pagination</param>
        /// <returns>List of interest payments</returns>
        Task<List<RzwSavingsInterestPayment>> GetInterestHistoryAsync(
            int userId,
            int? accountId = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            int pageNumber = 1,
            int pageSize = 50);

        /// <summary>
        /// Gets total count of interest payments for a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="accountId">Optional account ID filter</param>
        /// <param name="fromDate">Optional from date filter</param>
        /// <param name="toDate">Optional to date filter</param>
        /// <returns>Total count of interest payments</returns>
        Task<int> GetInterestHistoryCountAsync(
            int userId,
            int? accountId = null,
            DateTime? fromDate = null,
            DateTime? toDate = null);

        Task<List<RzwSavingsAccount>> GetAccountsForDailyInterestAsync();
    }
}
