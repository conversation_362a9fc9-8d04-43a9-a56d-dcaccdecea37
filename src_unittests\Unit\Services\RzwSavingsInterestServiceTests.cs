using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;

namespace RazeWinComTr.RewardTests.Unit.Services
{
    public class RzwSavingsInterestServiceTests : IDisposable
    {
        private readonly AppDbContext _context;
        private readonly Mock<ILogger<RzwSavingsInterestService>> _mockLogger;
        private readonly Mock<IRzwWalletBalanceManagementService> _mockBalanceService;
        private readonly Mock<IRzwSavingsPlanService> _mockPlanService;
        private readonly Mock<ITokenPriceService> _mockTokenPriceService;
        private readonly Mock<IStringLocalizer<SharedResource>> _mockLocalizer;
        private readonly RzwSavingsInterestService _service;

        public RzwSavingsInterestServiceTests()
        {
            // Setup in-memory database
            var options = new DbContextOptionsBuilder<AppDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .ConfigureWarnings(warnings => warnings.Ignore(InMemoryEventId.TransactionIgnoredWarning))
                .Options;
            _context = new AppDbContext(options);

            // Setup mocks
            _mockLogger = new Mock<ILogger<RzwSavingsInterestService>>();
            _mockBalanceService = new Mock<IRzwWalletBalanceManagementService>(MockBehavior.Strict);
            _mockPlanService = new Mock<IRzwSavingsPlanService>(MockBehavior.Strict);
            _mockTokenPriceService = new Mock<ITokenPriceService>(MockBehavior.Strict);
            _mockLocalizer = new Mock<IStringLocalizer<SharedResource>>();

            // Setup default localizer responses
            _mockLocalizer.Setup(x => x[It.IsAny<string>()])
                .Returns((string key) => new LocalizedString(key, key));

            // Create service instance
            _service = new RzwSavingsInterestService(
                _context,
                _mockBalanceService.Object,
                _mockPlanService.Object,
                _mockTokenPriceService.Object,
                _mockLocalizer.Object,
                _mockLogger.Object
            );
        }

        public void Dispose()
        {
            _context.Dispose();
        }

        #region Compound Interest Calculation Tests

        [Theory]
        [InlineData("Daily", 1000, 0.0003, 0.3)] // Daily plan: 1000 * 0.0003 = 0.3
        [InlineData("Monthly", 5000, 0.01, 1.666667)] // Monthly plan: 5000 * (0.01/30) = 1.666667
        [InlineData("Yearly", 10000, 0.15, 4.109589)] // Yearly plan: 10000 * (0.15/365) = 4.109589
        public async Task CalculateDailyInterestAsync_WithDifferentPlans_ShouldReturnCorrectDailyInterest(
            string termType, decimal principal, decimal rate, decimal expectedDailyInterest)
        {
            // Arrange
            var plan = new RzwSavingsPlan
            {
                Id = 1,
                Name = $"{termType} Plan",
                TermType = termType,
                InterestRate = rate,
                MinRzwAmount = 1000m,
                MaxRzwAmount = 100000m,
                IsActive = true
            };

            var account = new RzwSavingsAccount
            {
                InterestRate = rate,
                TermType = termType,
                TermDuration = termType switch
                {
                    "Daily" => 1,
                    "Monthly" => 30,
                    "Yearly" => 365,
                    _ => throw new ArgumentException("Invalid term type")
                },
                Id = 1,
                UserId = 1,
                PlanId = 1,
                RzwAmount = principal,
                Status = RzwSavingsStatus.Active,
                StartDate = DateTime.UtcNow,
                MaturityDate = DateTime.UtcNow.AddDays(30),
                CreatedDate = DateTime.UtcNow,
            };

            await _context.RzwSavingsPlans.AddAsync(plan);
            await _context.RzwSavingsAccounts.AddAsync(account);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.CalculateDailyInterestAsync(account);

            // Assert
            Assert.True(Math.Abs(result - expectedDailyInterest) < 0.001m,
                $"Expected {expectedDailyInterest}, got {result} for {termType} plan");
        }

        [Fact]
        public async Task CalculateCompoundInterestAsync_WithNonExistentAccount_ShouldReturnZero()
        {
            // Arrange
            var account = new RzwSavingsAccount
            {
                Id = 999, // Non-existent account
                UserId = 1,
                PlanId = 1,
                RzwAmount = 1000m,
                Status = RzwSavingsStatus.Active,
                StartDate = DateTime.UtcNow.AddDays(-30),
                MaturityDate = DateTime.UtcNow.AddDays(30),
                TermType = RzwSavingsTermType.Monthly,
                TermDuration = 60,
                InterestRate = 10m
            };

            // Act
            var result = await _service.CalculateDailyInterestAsync(account);

            // Assert
            Assert.Equal(0m, result);
        }

        [Fact]
        public async Task CalculateDailyInterestAsync_WithInactiveAccount_ShouldReturnZero()
        {
            // Arrange
            var plan = new RzwSavingsPlan
            {
                Id = 1,
                Name = "Daily Plan",
                TermType = "Daily",
                InterestRate = 0.0003m,
                MinRzwAmount = 1000m,
                MaxRzwAmount = 100000m,
                IsActive = true
            };

            var account = new RzwSavingsAccount
            {
                Id = 1,
                UserId = 1,
                PlanId = 1,
                RzwAmount = 1000m,
                Status = RzwSavingsStatus.Cancelled, // Inactive account
                StartDate = DateTime.UtcNow,
                MaturityDate = DateTime.UtcNow.AddDays(30),
                TermType = "Daily",
                TermDuration = 30,
                InterestRate = 0.0003m
            };

            await _context.RzwSavingsPlans.AddAsync(plan);
            await _context.RzwSavingsAccounts.AddAsync(account);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.CalculateDailyInterestAsync(account);

            // Assert
            Assert.Equal(0m, result);
        }

        #endregion

        #region Early Withdrawal Interest Tests

        [Fact]
        public async Task CalculateEarlyWithdrawalInterestAsync_WithActiveAccount_ShouldCalculatePartialInterest()
        {
            // Arrange
            var dailyPlan = new RzwSavingsPlan
            {
                Id = 1,
                Name = "Daily Plan",
                TermType = "Daily",
                TermDuration = 1,
                InterestRate = 0.0003m,
                MinRzwAmount = 100m,
                MaxRzwAmount = 100000m,
                IsActive = true
            };

            var monthlyPlan = new RzwSavingsPlan
            {
                Id = 2,
                Name = "Monthly Plan",
                TermType = "Monthly",
                TermDuration = 30,
                InterestRate = 0.01m,
                MinRzwAmount = 1000m,
                MaxRzwAmount = 100000m,
                IsActive = true
            };

            var account = new RzwSavingsAccount
            {
                Id = 1,
                UserId = 1,
                PlanId = 2, // Monthly plan
                RzwAmount = 5000m,
                Status = RzwSavingsStatus.Active,
                StartDate = DateTime.UtcNow.AddDays(-15), // 15 days ago
                MaturityDate = DateTime.UtcNow.AddDays(15), // 30 days total
                TermType = "Monthly",
                TermDuration = 30,
                InterestRate = 0.01m
            };

            await _context.RzwSavingsPlans.AddAsync(dailyPlan);
            await _context.RzwSavingsPlans.AddAsync(monthlyPlan);
            await _context.RzwSavingsAccounts.AddAsync(account);
            await _context.SaveChangesAsync();

            // Setup mock for eligible plans - daily plan is eligible for 15 days
            var allPlans = new List<RzwSavingsPlan> { dailyPlan, monthlyPlan };
            var eligiblePlans = new List<RzwSavingsPlan> { dailyPlan }; // Only daily plan is eligible for 15 days
            _mockPlanService.Setup(x => x.GetActivePlansAsync())
                .ReturnsAsync(allPlans);

            // Act
            var result = await _service.CalculateEarlyWithdrawalInterestAsync(account);

            // Assert
            Assert.True(result > 0, "Early withdrawal interest should be positive");
            Assert.True(result < 50m, "Early withdrawal interest should be less than full term interest");
        }

        [Fact]
        public async Task CalculateEarlyWithdrawalInterestAsync_WithNoEligiblePlans_ShouldReturnZero()
        {
            // Arrange
            var plan = new RzwSavingsPlan
            {
                Id = 1,
                Name = "Monthly Plan",
                TermType = "Monthly",
                TermDuration = 30,
                InterestRate = 0.01m,
                MinRzwAmount = 1000m,
                MaxRzwAmount = 100000m,
                IsActive = true
            };

            var account = new RzwSavingsAccount
            {
                Id = 1,
                UserId = 1,
                PlanId = 1,
                RzwAmount = 5000m,
                Status = RzwSavingsStatus.Active,
                StartDate = DateTime.UtcNow.AddDays(-5), // Only 5 days ago
                MaturityDate = DateTime.UtcNow.AddDays(25),
                TermType = "Monthly",
                TermDuration = 30,
                InterestRate = 0.01m
            };

            await _context.RzwSavingsPlans.AddAsync(plan);
            await _context.RzwSavingsAccounts.AddAsync(account);
            await _context.SaveChangesAsync();

            // Setup mock - no eligible plans for 5 days
            _mockPlanService.Setup(x => x.GetActivePlansAsync())
                .ReturnsAsync(new List<RzwSavingsPlan> { plan });

            // Act
            var result = await _service.CalculateEarlyWithdrawalInterestAsync(account);

            // Assert
            Assert.Equal(0m, result);
        }

        #endregion

        #region Maturity Interest Tests



        #endregion

        #region Daily Interest Calculation Tests

        [Theory]
        [InlineData("Daily", 0.0003, 0.3)] // Daily: 1000 * 0.0003 = 0.3
        [InlineData("Monthly", 0.01, 0.333333)] // Monthly: 1000 * (0.01/30) = 0.333333
        [InlineData("Yearly", 0.15, 0.410959)] // Yearly: 1000 * (0.15/365) = 0.410959
        public async Task CalculateDailyInterestAsync_WithDifferentTermTypes_ShouldReturnCorrectDailyInterestAmount(
            string termType, decimal annualRate, decimal expectedDailyInterestAmount)
        {
            // Arrange
            var plan = new RzwSavingsPlan
            {
                Id = 1,
                Name = $"{termType} Plan",
                TermType = termType,
                InterestRate = annualRate,
                MinRzwAmount = 1000m,
                MaxRzwAmount = 100000m,
                IsActive = true
            };

            var account = new RzwSavingsAccount
            {
                Id = 1,
                UserId = 1,
                PlanId = 1,
                RzwAmount = 1000m,
                Status = RzwSavingsStatus.Active,
                StartDate = DateTime.UtcNow,
                MaturityDate = DateTime.UtcNow.AddDays(30),
                TermType = termType,
                TermDuration = 30,
                InterestRate = annualRate
            };

            await _context.RzwSavingsPlans.AddAsync(plan);
            await _context.RzwSavingsAccounts.AddAsync(account);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.CalculateDailyInterestAsync(account);

            // Assert
            Assert.True(Math.Abs(result - expectedDailyInterestAmount) < 0.001m,
                $"Expected {expectedDailyInterestAmount}, got {result} for {termType} plan");
        }

        #endregion

        #region Interest Payment Tests

        [Fact]
        public async Task PayInterestAsync_WithValidData_ShouldCreateTradeRecord()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "***********",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "**********"
            };
            await _context.Users.AddAsync(user);

            var market = new Market { Id = 1, Coin = "RZW", Name = "RazeWin Token", ShortName = "RZW", PairCode = "RZWTRY" };
            await _context.Markets.AddAsync(market);

            var account = new RzwSavingsAccount
            {
                Id = 1,
                UserId = user.UserId,
                PlanId = 1,
                RzwAmount = 1000m,
                Status = RzwSavingsStatus.Active,
                StartDate = DateTime.UtcNow,
                MaturityDate = DateTime.UtcNow.AddDays(30),
                TermType = "Daily",
                TermDuration = 30,
                InterestRate = 0.01m
            };

            await _context.RzwSavingsAccounts.AddAsync(account);
            await _context.SaveChangesAsync();

            // Setup mock for balance service
            _mockBalanceService.Setup(x => x.AddRzwInterestAsync(account.Id, user.UserId, 25.5m, It.IsAny<string>(), _context))
                .ReturnsAsync(new Wallet { Id = 1, UserId = 1, CoinId = 1, Balance = 1000m });

            // Act
            var result = await _service.PayInterestAsync(account, 25.5m);

            // Assert
            Assert.True(result);

            // Verify that AddRzwInterestAsync was called with correct parameters
            _mockBalanceService.Verify(x => x.AddRzwInterestAsync(account.Id, user.UserId, 25.5m, It.IsAny<string>(), _context), Times.Once);

            // Verify account was updated
            var updatedAccount = await _context.RzwSavingsAccounts.FindAsync(account.Id);
            Assert.NotNull(updatedAccount);
            Assert.Equal(25.5m, updatedAccount.TotalEarnedRzw);
            Assert.True(updatedAccount.LastInterestDate.HasValue);
        }

        [Fact]
        public async Task PayInterestAsync_WithZeroAmount_ShouldReturnFalse()
        {
            // Arrange
            var account = new RzwSavingsAccount
            {
                Id = 1,
                UserId = 1,
                PlanId = 1,
                RzwAmount = 1000m,
                Status = RzwSavingsStatus.Active,
                StartDate = DateTime.UtcNow,
                MaturityDate = DateTime.UtcNow.AddDays(30),
                TermType = "Daily",
                TermDuration = 30,
                InterestRate = 0.01m
            };

            // Act
            var result = await _service.PayInterestAsync(account, 0m);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task PayInterestAsync_WithNegativeAmount_ShouldReturnFalse()
        {
            // Arrange
            var account = new RzwSavingsAccount
            {
                Id = 1,
                UserId = 1,
                PlanId = 1,
                RzwAmount = 1000m,
                Status = RzwSavingsStatus.Active,
                StartDate = DateTime.UtcNow,
                MaturityDate = DateTime.UtcNow.AddDays(30),
                TermType = "Daily",
                TermDuration = 30,
                InterestRate = 0.01m
            };

            // Act
            var result = await _service.PayInterestAsync(account, -10m);

            // Assert
            Assert.False(result);
        }

        #endregion
    }
}
