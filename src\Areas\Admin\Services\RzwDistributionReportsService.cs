using Microsoft.EntityFrameworkCore;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.RzwDistributionReports;
using System.Globalization;

namespace RazeWinComTr.Areas.Admin.Services
{
    /// <summary>
    /// Service for generating RZW distribution reports
    /// Provides comprehensive reporting on RZW distributions through savings interest and referral rewards
    /// </summary>
    public class RzwDistributionReportsService : IRzwDistributionReportsService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<RzwDistributionReportsService> _logger;

        public RzwDistributionReportsService(
            AppDbContext context,
            ILogger<RzwDistributionReportsService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Gets comprehensive RZW distribution report for a date range
        /// </summary>
        public async Task<RzwDistributionReportViewModel> GetDistributionReportAsync(DateTime startDate, DateTime endDate)
        {
            _logger.LogInformation("Generating RZW distribution report for period {StartDate} to {EndDate}", startDate, endDate);

            var report = new RzwDistributionReportViewModel
            {
                StartDate = startDate,
                EndDate = endDate
            };

            // Get savings interest distribution
            report.SavingsInterestDistribution = await GetSavingsInterestDistributionAsync(startDate, endDate);

            // Get referral reward distribution
            report.ReferralRewardDistribution = await GetReferralRewardDistributionAsync(startDate, endDate);

            // Calculate total
            report.TotalRzwDistributed = report.SavingsInterestDistribution.TotalRzwDistributed + 
                                       report.ReferralRewardDistribution.TotalRzwDistributed;

            // Get summary data
            report.DailyDistributionSummary = await GetDailyDistributionSummaryAsync(startDate, endDate);
            report.WeeklyDistributionSummary = await GetWeeklyDistributionSummaryAsync(startDate, endDate);
            report.MonthlyDistributionSummary = await GetMonthlyDistributionSummaryAsync(startDate, endDate);
            report.YearlyDistributionSummary = await GetYearlyDistributionSummaryAsync(startDate, endDate);

            _logger.LogInformation("RZW distribution report generated. Total RZW distributed: {TotalRzw}", report.TotalRzwDistributed);

            return report;
        }

        /// <summary>
        /// Gets savings interest distribution data for a date range
        /// </summary>
        public async Task<SavingsInterestDistributionViewModel> GetSavingsInterestDistributionAsync(DateTime startDate, DateTime endDate)
        {
            var interestPayments = await _context.RzwSavingsInterestPayments
                .Include(p => p.RzwSavingsAccount)
                .Where(p => p.PaymentDate >= startDate && p.PaymentDate <= endDate)
                .ToListAsync();

            var result = new SavingsInterestDistributionViewModel
            {
                TotalRzwDistributed = interestPayments.Sum(p => p.RzwAmount),
                TotalPaymentCount = interestPayments.Count,
                UniqueAccountCount = interestPayments.Select(p => p.RzwSavingsAccountId).Distinct().Count()
            };

            // Daily breakdown
            result.DailyBreakdown = interestPayments
                .GroupBy(p => p.PaymentDate.Date)
                .Select(g => new DailySavingsInterestViewModel
                {
                    Date = g.Key,
                    TotalRzwDistributed = g.Sum(p => p.RzwAmount),
                    PaymentCount = g.Count(),
                    UniqueAccountCount = g.Select(p => p.RzwSavingsAccountId).Distinct().Count()
                })
                .OrderBy(d => d.Date)
                .ToList();

            // Term type breakdown
            var termTypeGroups = interestPayments
                .Where(p => p.RzwSavingsAccount != null)
                .GroupBy(p => p.RzwSavingsAccount!.TermType)
                .ToList();

            result.TermTypeBreakdown = termTypeGroups
                .Select(g => new TermTypeBreakdownViewModel
                {
                    TermType = g.Key,
                    TotalRzwDistributed = g.Sum(p => p.RzwAmount),
                    PaymentCount = g.Count(),
                    UniqueAccountCount = g.Select(p => p.RzwSavingsAccountId).Distinct().Count(),
                    Percentage = result.TotalRzwDistributed > 0 
                        ? Math.Round((g.Sum(p => p.RzwAmount) / result.TotalRzwDistributed) * 100, 2) 
                        : 0
                })
                .OrderByDescending(t => t.TotalRzwDistributed)
                .ToList();

            return result;
        }

        /// <summary>
        /// Gets referral reward distribution data for a date range
        /// </summary>
        public async Task<ReferralRewardDistributionViewModel> GetReferralRewardDistributionAsync(DateTime startDate, DateTime endDate)
        {
            var referralRewards = await _context.ReferralRewards
                .Include(r => r.Package)
                .Where(r => r.CreatedDate >= startDate && r.CreatedDate <= endDate)
                .ToListAsync();

            var result = new ReferralRewardDistributionViewModel
            {
                TotalRzwDistributed = referralRewards.Sum(r => r.RzwAmount),
                TotalRewardCount = referralRewards.Count,
                UniqueUserCount = referralRewards.Select(r => r.UserId).Distinct().Count(),
                UniqueReferredUserCount = referralRewards.Select(r => r.ReferredUserId).Distinct().Count()
            };

            // Daily breakdown
            result.DailyBreakdown = referralRewards
                .GroupBy(r => r.CreatedDate.Date)
                .Select(g => new DailyReferralRewardViewModel
                {
                    Date = g.Key,
                    TotalRzwDistributed = g.Sum(r => r.RzwAmount),
                    RewardCount = g.Count(),
                    UniqueUserCount = g.Select(r => r.UserId).Distinct().Count()
                })
                .OrderBy(d => d.Date)
                .ToList();

            // Level breakdown
            result.LevelBreakdown = referralRewards
                .GroupBy(r => r.Level)
                .Select(g => new ReferralLevelBreakdownViewModel
                {
                    Level = g.Key,
                    TotalRzwDistributed = g.Sum(r => r.RzwAmount),
                    RewardCount = g.Count(),
                    Percentage = result.TotalRzwDistributed > 0 
                        ? Math.Round((g.Sum(r => r.RzwAmount) / result.TotalRzwDistributed) * 100, 2) 
                        : 0
                })
                .OrderBy(l => l.Level)
                .ToList();

            // Package breakdown
            var packageGroups = referralRewards
                .Where(r => r.Package != null)
                .GroupBy(r => r.Package!.Name)
                .ToList();

            result.PackageBreakdown = packageGroups
                .Select(g => new PackageBreakdownViewModel
                {
                    PackageName = g.Key,
                    TotalRzwDistributed = g.Sum(r => r.RzwAmount),
                    RewardCount = g.Count(),
                    Percentage = result.TotalRzwDistributed > 0 
                        ? Math.Round((g.Sum(r => r.RzwAmount) / result.TotalRzwDistributed) * 100, 2) 
                        : 0
                })
                .OrderByDescending(p => p.TotalRzwDistributed)
                .ToList();

            // Reward type breakdown
            result.RewardTypeBreakdown = referralRewards
                .GroupBy(r => r.RewardType)
                .Select(g => new RewardTypeBreakdownViewModel
                {
                    RewardType = g.Key,
                    TotalRzwDistributed = g.Sum(r => r.RzwAmount),
                    RewardCount = g.Count(),
                    Percentage = result.TotalRzwDistributed > 0 
                        ? Math.Round((g.Sum(r => r.RzwAmount) / result.TotalRzwDistributed) * 100, 2) 
                        : 0
                })
                .OrderByDescending(t => t.TotalRzwDistributed)
                .ToList();

            return result;
        }

        /// <summary>
        /// Gets daily RZW distribution summary for a date range
        /// </summary>
        public async Task<List<DailyDistributionSummaryViewModel>> GetDailyDistributionSummaryAsync(DateTime startDate, DateTime endDate)
        {
            // Get savings interest payments grouped by date
            var savingsInterestByDate = await _context.RzwSavingsInterestPayments
                .Where(p => p.PaymentDate >= startDate && p.PaymentDate <= endDate)
                .GroupBy(p => p.PaymentDate.Date)
                .Select(g => new { Date = g.Key, TotalRzw = g.Sum(p => p.RzwAmount), Count = g.Count() })
                .ToListAsync();

            // Get referral rewards grouped by date
            var referralRewardsByDate = await _context.ReferralRewards
                .Where(r => r.CreatedDate >= startDate && r.CreatedDate <= endDate)
                .GroupBy(r => r.CreatedDate.Date)
                .Select(g => new { Date = g.Key, TotalRzw = g.Sum(r => r.RzwAmount), Count = g.Count() })
                .ToListAsync();

            // Combine and create daily summaries
            var allDates = savingsInterestByDate.Select(s => s.Date)
                .Union(referralRewardsByDate.Select(r => r.Date))
                .Distinct()
                .OrderBy(d => d);

            var result = new List<DailyDistributionSummaryViewModel>();

            foreach (var date in allDates)
            {
                var savingsData = savingsInterestByDate.FirstOrDefault(s => s.Date == date);
                var referralData = referralRewardsByDate.FirstOrDefault(r => r.Date == date);

                result.Add(new DailyDistributionSummaryViewModel
                {
                    Date = date,
                    SavingsInterestRzw = savingsData?.TotalRzw ?? 0,
                    ReferralRewardRzw = referralData?.TotalRzw ?? 0,
                    SavingsInterestPaymentCount = savingsData?.Count ?? 0,
                    ReferralRewardPaymentCount = referralData?.Count ?? 0
                });
            }

            return result;
        }

        /// <summary>
        /// Gets weekly RZW distribution summary for a date range
        /// </summary>
        public async Task<List<WeeklyDistributionSummaryViewModel>> GetWeeklyDistributionSummaryAsync(DateTime startDate, DateTime endDate)
        {
            var calendar = CultureInfo.CurrentCulture.Calendar;
            var dateRule = CalendarWeekRule.FirstDay;
            var firstDayOfWeek = DayOfWeek.Monday;

            // Get all daily summaries first
            var dailySummaries = await GetDailyDistributionSummaryAsync(startDate, endDate);

            // Group by week
            var weeklyGroups = dailySummaries
                .GroupBy(d => new
                {
                    Year = calendar.GetYear(d.Date),
                    Week = calendar.GetWeekOfYear(d.Date, dateRule, firstDayOfWeek)
                })
                .ToList();

            var result = new List<WeeklyDistributionSummaryViewModel>();

            foreach (var group in weeklyGroups)
            {
                var weekDates = group.Select(g => g.Date).ToList();
                var weekStartDate = weekDates.Min();
                var weekEndDate = weekDates.Max();

                result.Add(new WeeklyDistributionSummaryViewModel
                {
                    Year = group.Key.Year,
                    WeekNumber = group.Key.Week,
                    WeekStartDate = weekStartDate,
                    WeekEndDate = weekEndDate,
                    SavingsInterestRzw = group.Sum(g => g.SavingsInterestRzw),
                    ReferralRewardRzw = group.Sum(g => g.ReferralRewardRzw),
                    SavingsInterestPaymentCount = group.Sum(g => g.SavingsInterestPaymentCount),
                    ReferralRewardPaymentCount = group.Sum(g => g.ReferralRewardPaymentCount)
                });
            }

            return result.OrderBy(w => w.Year).ThenBy(w => w.WeekNumber).ToList();
        }

        /// <summary>
        /// Gets monthly RZW distribution summary for a date range
        /// </summary>
        public async Task<List<MonthlyDistributionSummaryViewModel>> GetMonthlyDistributionSummaryAsync(DateTime startDate, DateTime endDate)
        {
            // Get all daily summaries first
            var dailySummaries = await GetDailyDistributionSummaryAsync(startDate, endDate);

            // Group by month
            var monthlyGroups = dailySummaries
                .GroupBy(d => new { Year = d.Date.Year, Month = d.Date.Month })
                .ToList();

            var result = new List<MonthlyDistributionSummaryViewModel>();

            foreach (var group in monthlyGroups)
            {
                result.Add(new MonthlyDistributionSummaryViewModel
                {
                    Year = group.Key.Year,
                    Month = group.Key.Month,
                    SavingsInterestRzw = group.Sum(g => g.SavingsInterestRzw),
                    ReferralRewardRzw = group.Sum(g => g.ReferralRewardRzw),
                    SavingsInterestPaymentCount = group.Sum(g => g.SavingsInterestPaymentCount),
                    ReferralRewardPaymentCount = group.Sum(g => g.ReferralRewardPaymentCount)
                });
            }

            return result.OrderBy(m => m.Year).ThenBy(m => m.Month).ToList();
        }

        /// <summary>
        /// Gets yearly RZW distribution summary for a date range
        /// </summary>
        public async Task<List<YearlyDistributionSummaryViewModel>> GetYearlyDistributionSummaryAsync(DateTime startDate, DateTime endDate)
        {
            // Get all daily summaries first
            var dailySummaries = await GetDailyDistributionSummaryAsync(startDate, endDate);

            // Group by year
            var yearlyGroups = dailySummaries
                .GroupBy(d => d.Date.Year)
                .ToList();

            var result = new List<YearlyDistributionSummaryViewModel>();

            foreach (var group in yearlyGroups)
            {
                result.Add(new YearlyDistributionSummaryViewModel
                {
                    Year = group.Key,
                    SavingsInterestRzw = group.Sum(g => g.SavingsInterestRzw),
                    ReferralRewardRzw = group.Sum(g => g.ReferralRewardRzw),
                    SavingsInterestPaymentCount = group.Sum(g => g.SavingsInterestPaymentCount),
                    ReferralRewardPaymentCount = group.Sum(g => g.ReferralRewardPaymentCount)
                });
            }

            return result.OrderBy(y => y.Year).ToList();
        }

        /// <summary>
        /// Gets total RZW distributed across all sources for a date range
        /// </summary>
        public async Task<decimal> GetTotalRzwDistributedAsync(DateTime startDate, DateTime endDate)
        {
            // Get total from savings interest
            var savingsInterestTotal = await _context.RzwSavingsInterestPayments
                .Where(p => p.PaymentDate >= startDate && p.PaymentDate <= endDate)
                .SumAsync(p => p.RzwAmount);

            // Get total from referral rewards
            var referralRewardTotal = await _context.ReferralRewards
                .Where(r => r.CreatedDate >= startDate && r.CreatedDate <= endDate)
                .SumAsync(r => r.RzwAmount);

            return savingsInterestTotal + referralRewardTotal;
        }
    }
