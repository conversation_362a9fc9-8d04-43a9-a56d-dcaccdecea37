@page
@using Microsoft.Extensions.Localization
@model RazeWinComTr.Areas.Admin.Pages.RzwDistributionReports.IndexModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["RZW Distribution Reports"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["RZW Distribution Reports"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item active">@L["RZW Distribution Reports"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <!-- Filter Form -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">@L["Report Filters"]</h3>
            </div>
            <div class="card-body">
                <form method="get">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="StartDate">@L["Start Date"]</label>
                                <input type="date" class="form-control" id="StartDate" name="StartDate" 
                                       value="@Model.StartDate?.ToString("yyyy-MM-dd")" />
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="EndDate">@L["End Date"]</label>
                                <input type="date" class="form-control" id="EndDate" name="EndDate" 
                                       value="@Model.EndDate?.ToString("yyyy-MM-dd")" />
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="ReportType">@L["Report Type"]</label>
                                <select class="form-control" id="ReportType" name="ReportType">
                                    <option value="daily" selected="@(Model.ReportType == "daily")">@L["Daily"]</option>
                                    <option value="weekly" selected="@(Model.ReportType == "weekly")">@L["Weekly"]</option>
                                    <option value="monthly" selected="@(Model.ReportType == "monthly")">@L["Monthly"]</option>
                                    <option value="yearly" selected="@(Model.ReportType == "yearly")">@L["Yearly"]</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> @L["Generate Report"]
                                    </button>
                                    @if (Model.Report != null)
                                    {
                                        <a href="?handler=Export&StartDate=@Model.StartDate?.ToString("yyyy-MM-dd")&EndDate=@Model.EndDate?.ToString("yyyy-MM-dd")&ReportType=@Model.ReportType" 
                                           class="btn btn-success">
                                            <i class="fas fa-download"></i> @L["Export CSV"]
                                        </a>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        @if (Model.Report != null)
        {
            <!-- Summary Cards -->
            <div class="row">
                <div class="col-lg-4 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3>@Model.Report.TotalRzwDistributed.ToString("N8")</h3>
                            <p>@L["Total RZW Distributed"]</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-coins"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3>@Model.Report.SavingsInterestDistribution.TotalRzwDistributed.ToString("N8")</h3>
                            <p>@L["Savings Interest"] (@Model.Report.SavingsInterestPercentage.ToString("N2")%)</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-piggy-bank"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>@Model.Report.ReferralRewardDistribution.TotalRzwDistributed.ToString("N8")</h3>
                            <p>@L["Referral Rewards"] (@Model.Report.ReferralRewardPercentage.ToString("N2")%)</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Distribution Chart -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">@L["RZW Distribution Breakdown"]</h3>
                </div>
                <div class="card-body">
                    <canvas id="distributionChart" style="height: 400px;"></canvas>
                </div>
            </div>

            <!-- Detailed Breakdown -->
            <div class="row">
                <!-- Savings Interest Details -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">@L["Savings Interest Details"]</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="description-block border-right">
                                        <span class="description-percentage text-success">
                                            <i class="fas fa-caret-up"></i> @Model.Report.SavingsInterestDistribution.TotalPaymentCount
                                        </span>
                                        <h5 class="description-header">@L["Total Payments"]</h5>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="description-block">
                                        <span class="description-percentage text-info">
                                            <i class="fas fa-users"></i> @Model.Report.SavingsInterestDistribution.UniqueAccountCount
                                        </span>
                                        <h5 class="description-header">@L["Unique Accounts"]</h5>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="description-block">
                                        <h5 class="description-header">@Model.Report.SavingsInterestDistribution.AveragePaymentAmount.ToString("N8")</h5>
                                        <span class="description-text">@L["Average Payment Amount"]</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Referral Rewards Details -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">@L["Referral Rewards Details"]</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="description-block border-right">
                                        <span class="description-percentage text-warning">
                                            <i class="fas fa-caret-up"></i> @Model.Report.ReferralRewardDistribution.TotalRewardCount
                                        </span>
                                        <h5 class="description-header">@L["Total Rewards"]</h5>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="description-block">
                                        <span class="description-percentage text-info">
                                            <i class="fas fa-users"></i> @Model.Report.ReferralRewardDistribution.UniqueUserCount
                                        </span>
                                        <h5 class="description-header">@L["Unique Users"]</h5>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="description-block">
                                        <h5 class="description-header">@Model.Report.ReferralRewardDistribution.AverageRewardAmount.ToString("N8")</h5>
                                        <span class="description-text">@L["Average Reward Amount"]</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Time-based Distribution Table -->
            @if (Model.ReportType == "daily" && Model.Report.DailyDistributionSummary.Any())
            {
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">@L["Daily Distribution Summary"]</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>@L["Date"]</th>
                                        <th>@L["Savings Interest RZW"]</th>
                                        <th>@L["Referral Reward RZW"]</th>
                                        <th>@L["Total RZW"]</th>
                                        <th>@L["Total Payments"]</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var daily in Model.Report.DailyDistributionSummary.OrderByDescending(d => d.Date))
                                    {
                                        <tr>
                                            <td>@daily.FormattedDate</td>
                                            <td>@daily.SavingsInterestRzw.ToString("N8")</td>
                                            <td>@daily.ReferralRewardRzw.ToString("N8")</td>
                                            <td><strong>@daily.TotalRzwDistributed.ToString("N8")</strong></td>
                                            <td>@daily.TotalPaymentCount</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }

            @if (Model.ReportType == "weekly" && Model.Report.WeeklyDistributionSummary.Any())
            {
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">@L["Weekly Distribution Summary"]</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>@L["Week"]</th>
                                        <th>@L["Period"]</th>
                                        <th>@L["Savings Interest RZW"]</th>
                                        <th>@L["Referral Reward RZW"]</th>
                                        <th>@L["Total RZW"]</th>
                                        <th>@L["Total Payments"]</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var weekly in Model.Report.WeeklyDistributionSummary.OrderByDescending(w => w.Year).ThenByDescending(w => w.WeekNumber))
                                    {
                                        <tr>
                                            <td>@weekly.WeekIdentifier</td>
                                            <td>@weekly.FormattedWeekRange</td>
                                            <td>@weekly.SavingsInterestRzw.ToString("N8")</td>
                                            <td>@weekly.ReferralRewardRzw.ToString("N8")</td>
                                            <td><strong>@weekly.TotalRzwDistributed.ToString("N8")</strong></td>
                                            <td>@weekly.TotalPaymentCount</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }

            @if (Model.ReportType == "monthly" && Model.Report.MonthlyDistributionSummary.Any())
            {
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">@L["Monthly Distribution Summary"]</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>@L["Month"]</th>
                                        <th>@L["Savings Interest RZW"]</th>
                                        <th>@L["Referral Reward RZW"]</th>
                                        <th>@L["Total RZW"]</th>
                                        <th>@L["Total Payments"]</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var monthly in Model.Report.MonthlyDistributionSummary.OrderByDescending(m => m.Year).ThenByDescending(m => m.Month))
                                    {
                                        <tr>
                                            <td>@monthly.MonthIdentifier</td>
                                            <td>@monthly.SavingsInterestRzw.ToString("N8")</td>
                                            <td>@monthly.ReferralRewardRzw.ToString("N8")</td>
                                            <td><strong>@monthly.TotalRzwDistributed.ToString("N8")</strong></td>
                                            <td>@monthly.TotalPaymentCount</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }

            @if (Model.ReportType == "yearly" && Model.Report.YearlyDistributionSummary.Any())
            {
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">@L["Yearly Distribution Summary"]</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>@L["Year"]</th>
                                        <th>@L["Savings Interest RZW"]</th>
                                        <th>@L["Referral Reward RZW"]</th>
                                        <th>@L["Total RZW"]</th>
                                        <th>@L["Total Payments"]</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var yearly in Model.Report.YearlyDistributionSummary.OrderByDescending(y => y.Year))
                                    {
                                        <tr>
                                            <td>@yearly.FormattedYear</td>
                                            <td>@yearly.SavingsInterestRzw.ToString("N8")</td>
                                            <td>@yearly.ReferralRewardRzw.ToString("N8")</td>
                                            <td><strong>@yearly.TotalRzwDistributed.ToString("N8")</strong></td>
                                            <td>@yearly.TotalPaymentCount</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <div class="card">
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        @L["Please select date range and click Generate Report to view RZW distribution data."]
                    </div>
                </div>
            </div>
        }
    </div>
</section>

@section Scripts {
    @if (Model.Report != null)
    {
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            // Distribution Pie Chart
            const ctx = document.getElementById('distributionChart').getContext('2d');
            const distributionChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['@L["Savings Interest"]', '@L["Referral Rewards"]'],
                    datasets: [{
                        data: [@Model.Report.SavingsInterestDistribution.TotalRzwDistributed, @Model.Report.ReferralRewardDistribution.TotalRzwDistributed],
                        backgroundColor: [
                            '#28a745',
                            '#ffc107'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.parsed;
                                    const total = @Model.Report.TotalRzwDistributed;
                                    const percentage = ((value / total) * 100).toFixed(2);
                                    return label + ': ' + value.toFixed(8) + ' RZW (' + percentage + '%)';
                                }
                            }
                        }
                    }
                }
            });
        </script>
    }
}
